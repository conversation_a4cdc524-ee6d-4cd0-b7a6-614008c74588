import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { FlcModalService } from 'fl-common-lib';
import { ReceivableService } from '../receivable-service';
import {
  ReceivableDetail,
  PageEditStatusEnum,
  ApiResponse,
  OperateButtonEnum,
  PageEditModeEnum,
  ReceivableOrderStatusEnum,
  V1BillsReceivableDetailLine,
  ReceivableDetailParams,
} from '../models/receivable-detail.interface';

import { ReceivableManagementLoanDetailComponent } from '../components/loan-detail/receivable-management-loan-detail.component';
import { ReceivableManagementBaseinfoComponent } from '../components/baseinfo/receivable-management-baseinfo.component';
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';
import { SettlementReturnModalComponent } from '../../manufacture-settlement/components/settlement-return-modal/settlement-return-modal.component';

@Component({
  selector: 'flss-receivable-management-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class ReceivableDetailComponent implements OnInit, OnDestroy {
  @ViewChild(ReceivableManagementBaseinfoComponent) baseinfoForm?: ReceivableManagementBaseinfoComponent;
  @ViewChild(ReceivableManagementLoanDetailComponent) loanDetailForm?: ReceivableManagementLoanDetailComponent;
  editMode: PageEditModeEnum = PageEditModeEnum.add;
  detailInfo?: ReceivableDetail;
  id: string | number = this._route.snapshot.params.id;
  subscribeKey = 'receivable-detail';


  // 表单相关
  receivableDetailForm!: FormGroup;

  // 统计数据
  receivableStatisticData: any = {};
  adjustmentStatisticData: any = {};

  // 调整项目相关
  checkedAdjustment: any[] = [];
  checkedAdjustmentIds: any[] = [];

  constructor(
    private _route: ActivatedRoute,
    private _router: Router,
    private _msg: NzMessageService,
    private _notifyService: NzNotificationService,
    private _modalService: NzModalService,
    private _flcModalService: FlcModalService,
    private _service: ReceivableService,
    private _fb: FormBuilder
  ) {
    this.initForm();
  }

  private initForm() {
    this.receivableDetailForm = this._fb.group({
      outbound_order_list: this._fb.array([]),
    });
  }

  ngOnInit() {
    
    if (this.id !== PageEditModeEnum.add) {
      this.editMode = PageEditModeEnum.read;
      this.getDetail(Number(this.id));
    }
  }

  ngOnDestroy() {
    // 清理订阅
    this._service.removeSubjectListener(this.subscribeKey);
  }

  /**
   * 处理删除明细行事件
   * 当应收单明细行被删除时，保存应收单数据
   */
  handleDeleteLine(event: {
    deletedLineIds: string[];
    receivableId?: string;
    customerId?: string;
  }) {
    console.log('删除明细行事件:', event);

    // 直接调用现有的保存方法
    this.onSaveOrCommit();
  }

  private getDetail(id: number) {
    this._service.getDetail(id.toString()).subscribe({
      next: (res: ApiResponse<ReceivableDetail>) => {
        if (res.code === 200) {
          this.detailInfo = res.data;
        }
      },
      error: () => {
        this._router.navigate(['..'], { relativeTo: this._route });
      },
    });
  }

  onAction(action: OperateButtonEnum) {
    switch (action) {
      case OperateButtonEnum.back:
        this.onBack();
        break;
      case OperateButtonEnum.edit:
        this.editMode = PageEditModeEnum.edit;
        break;
      case OperateButtonEnum.save:
        this.onSave();
        break;
      case OperateButtonEnum.commit:
        this.onCommit();
        break;
      case OperateButtonEnum.cancel:
        this.onCancel();
        break;
      case OperateButtonEnum.cancelSettlement:
        this.onCancelSettlement();
        break;
      case OperateButtonEnum.pass:
        this.onPass();
        break;
      case OperateButtonEnum.modify:
        this.onModify();
        break;
      case OperateButtonEnum.export:
        // this.onExport();
        break;
      case OperateButtonEnum.unBindInvoice:
        // this.unBindInvoice();
        break;
    }
  }

  private onBack() {
    this._router.navigate(['..'], { relativeTo: this._route });
  }

  private onSave() {
    if (!this.baseinfoForm?.baseInfoForm.get('code')?.value) {
      this._notifyService.error('应收单号不能为空', '');
      return;
    }
    this.onSaveOrCommit();
  }

  private onCommit() {
    const _baseFormInvalid = this.baseinfoForm?.formInvalid();
    const _loanDetailInvalid = !this.loanDetailForm?.validateForm();

    if (_baseFormInvalid) return;

    if (!this.loanDetailForm?.outbound_order_list?.getRawValue()?.length) {
      this._notifyService.error('', '应收明细不能为空');
      return;
    }

    if (_loanDetailInvalid) {
      this._notifyService.error('', '应收明细中数据有误，请检查');
      return;
    }

    this.onSaveOrCommit(true,true);
  }

  private onSaveOrCommit(is_commit = false,getDetail = false) {
    let baseinfo:any = this.baseinfoForm?.getFormValue();
    const params = {
      id: this.id === PageEditModeEnum.add ? '0' : this.id,
      ...baseinfo,
      ...this.loanDetailForm?.getFormData(),
      invoice_tax_rate:String(baseinfo.invoice_tax_rate),
      is_commit,
    };

    const apiCall = is_commit
      ? this._service.submitReceivable(params)
      : this._service.saveReceivable(params);

    apiCall.subscribe((res: ApiResponse<any>) => {
      if (res.code === 200) {
        if (!getDetail) return
        this._msg.success(is_commit ? '提交成功' : '暂存成功');
        res.data.id && (this.id = res.data.id);
        const _url = window.location.href;
        window.history.replaceState({}, '', _url.replace('list/add', 'list/' + this.id));
        this.editMode =  PageEditModeEnum.read 
        this.getDetail(Number(this.id));
        this._service.refreshEvent.emit();
      }
    });
  }

  private onCancel() {
    if (this.editMode === PageEditModeEnum.edit) {
      this.editMode = PageEditModeEnum.read;
      this.getDetail(Number(this.id));
    } else {
      this.onBack();
    }
  }

  private onCancelSettlement() {
    const ref = this._flcModalService.confirmCancel({
      title: '确认取消结算',
      content: '取消后将无法恢复，是否确认取消？',
    });
        // TODO: 实现取消结算接口调用
        this._msg.success('取消结算成功');
        this.onBack();
  }

  private onPass() {
    const ref = this._flcModalService.confirmCancel({
      title: '确认审核通过',
      content: '审核通过后将无法修改，是否确认通过？',})
        ref.afterClose.subscribe((res) => {
      if (res) {
        this._service.batchPass([this.id]).subscribe((res: ApiResponse<any>) => {
          if (res.code === 200) {
            this._msg.success('审核通过成功');
            this.getDetail(Number(this.id));
            this._service.refreshEvent.emit();
          }
        });
    }});
  }

  private onModify() {
    this._modalService.create({
      nzContent: SettlementReturnModalComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzOnOk: (comp) => {
        const _value = comp.formGroup.getRawValue();
        this._service.batchReject([Number(this.id)], _value.reason).subscribe((result) => {
          if (result.data) {
            this.getDetail(Number(this.id));
            this._service.refreshEvent.emit();
            this._msg.success('退回成功');
          }
        });
      },
    });
  }

  /**
   * 检查表单是否有效
   */
  isFormValid(): boolean {
    const baseFormValid = !this.baseinfoForm?.formInvalid();
    const loanDetailValid = this.loanDetailForm?.validateForm();
    return baseFormValid && !!loanDetailValid;
  }

  /**
   * 获取完整的表单数据
   */
  getCompleteFormData(): any {
    return {
      ...this.baseinfoForm?.getFormValue(),
      ...this.loanDetailForm?.getFormData(),
    };
  }

  /**
   * 重置表单
   */
  resetForm() {
    this.baseinfoForm?.baseInfoForm.reset();
    this.loanDetailForm?.receivableForm.reset();
    this.detailInfo = undefined;
  }

  /**
   * 检查是否有未保存的更改
   */
  hasUnsavedChanges(): boolean {
    return this.baseinfoForm?.baseInfoForm.dirty || this.loanDetailForm?.receivableForm.dirty || false;
  }

  /**
   * 获取当前状态的按钮配置
   */
  getButtonsForCurrentState(): OperateButtonEnum[] {
    if (this.editMode === PageEditModeEnum.add) {
      return [OperateButtonEnum.cancel, OperateButtonEnum.save, OperateButtonEnum.commit];
    }

    if (this.editMode === PageEditModeEnum.edit) {
      if (this.detailInfo?.status === ReceivableOrderStatusEnum.waitCommit) {
        return [OperateButtonEnum.cancel, OperateButtonEnum.save, OperateButtonEnum.commit];
      }
      return [OperateButtonEnum.cancel, OperateButtonEnum.commit];
    }

    // 只读模式
    const buttons: OperateButtonEnum[] = [OperateButtonEnum.back];

    if (this.detailInfo?.status === ReceivableOrderStatusEnum.waitCommit) {
      buttons.push(OperateButtonEnum.edit);
    }

    if (this.detailInfo?.status === ReceivableOrderStatusEnum.waitCheck) {
      buttons.push(OperateButtonEnum.modify, OperateButtonEnum.pass);
    }

    return buttons;
  }

  test() {
  }
}
